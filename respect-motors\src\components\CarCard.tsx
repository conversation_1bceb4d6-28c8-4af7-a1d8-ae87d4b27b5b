'use client';

import { motion } from 'framer-motion';
import { Eye, Heart, Zap, Gauge, Calendar } from 'lucide-react';

interface CarCardProps {
  car: {
    id: string;
    name: string;
    brand: string;
    year: number;
    price: string;
    image: string;
    specs: {
      engine: string;
      power: string;
      acceleration: string;
    };
    featured?: boolean;
  };
}

const CarCard = ({ car }: CarCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      whileHover={{ y: -10 }}
      className="group relative bg-luxury-darkGray rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500"
    >
      {/* Featured Badge */}
      {car.featured && (
        <div className="absolute top-4 left-4 z-10 bg-gradient-to-r from-luxury-gold to-luxury-darkGold text-luxury-charcoal px-3 py-1 rounded-full text-xs font-semibold">
          FEATURED
        </div>
      )}

      {/* Image Container */}
      <div className="relative h-64 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-charcoal to-luxury-darkGray"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-32 h-20 bg-luxury-gold/20 rounded-lg flex items-center justify-center">
            <span className="text-luxury-gold font-semibold text-sm">
              {car.brand} {car.name}
            </span>
          </div>
        </div>
        
        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <div className="flex space-x-4">
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="bg-luxury-gold text-luxury-charcoal p-3 rounded-full hover:bg-luxury-darkGold transition-colors duration-300"
            >
              <Eye className="w-5 h-5" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="bg-white/20 text-white p-3 rounded-full hover:bg-white/30 transition-colors duration-300"
            >
              <Heart className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-xl font-luxury font-semibold text-white group-hover:text-luxury-gold transition-colors duration-300">
              {car.brand} {car.name}
            </h3>
            <span className="text-luxury-gold font-medium flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              {car.year}
            </span>
          </div>
          <div className="text-2xl font-bold text-luxury-gold">
            {car.price}
          </div>
        </div>

        {/* Specs */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center text-gray-300">
            <Zap className="w-4 h-4 mr-3 text-luxury-gold" />
            <span className="text-sm">{car.specs.engine}</span>
          </div>
          <div className="flex items-center text-gray-300">
            <Gauge className="w-4 h-4 mr-3 text-luxury-gold" />
            <span className="text-sm">{car.specs.power}</span>
          </div>
          <div className="flex items-center text-gray-300">
            <Zap className="w-4 h-4 mr-3 text-luxury-gold" />
            <span className="text-sm">0-60mph: {car.specs.acceleration}</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1 bg-gradient-to-r from-luxury-gold to-luxury-darkGold text-luxury-charcoal py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300"
          >
            View Details
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="flex-1 border border-luxury-gold text-luxury-gold py-3 rounded-lg font-semibold hover:bg-luxury-gold hover:text-luxury-charcoal transition-all duration-300"
          >
            Test Drive
          </motion.button>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-luxury-gold/10 to-transparent rounded-bl-full"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-luxury-gold/5 to-transparent rounded-tr-full"></div>
    </motion.div>
  );
};

export default CarCard;
