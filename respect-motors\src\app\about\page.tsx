'use client';

import { motion } from 'framer-motion';
import { Award, Users, Globe, Heart, Target, Zap } from 'lucide-react';

export default function About() {
  const stats = [
    { value: '15+', label: 'Years of Excellence' },
    { value: '500+', label: 'Luxury Vehicles Sold' },
    { value: '10K+', label: 'Satisfied Customers' },
    { value: '50+', label: 'Premium Brands' },
  ];

  const values = [
    {
      icon: Heart,
      title: 'Passion',
      description: 'Our love for automotive excellence drives everything we do'
    },
    {
      icon: Target,
      title: 'Precision',
      description: 'Meticulous attention to detail in every aspect of our service'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Embracing cutting-edge technology and modern solutions'
    },
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Connecting luxury car enthusiasts worldwide'
    }
  ];

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-luxury-charcoal via-luxury-darkGray to-black">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-5xl md:text-7xl font-luxury font-bold text-white mb-6">
              About <span className="text-gradient">Respect Motors</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
              Where passion meets precision in the world of luxury automobiles. 
              Since 2009, we've been redefining automotive excellence.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-20 bg-luxury-darkGray">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl font-luxury font-bold text-white mb-6">
                Our <span className="text-gradient">Story</span>
              </h2>
              <div className="space-y-6 text-gray-300 leading-relaxed">
                <p>
                  Founded in 2009 by automotive enthusiast Marcus Respect, Respect Motors began 
                  as a vision to create the ultimate luxury car buying experience. What started 
                  as a small showroom has evolved into one of the most prestigious automotive 
                  destinations in the world.
                </p>
                <p>
                  Our journey has been marked by an unwavering commitment to excellence, 
                  authenticity, and customer satisfaction. We believe that purchasing a luxury 
                  vehicle should be as extraordinary as the car itself.
                </p>
                <p>
                  Today, we proudly serve discerning clients globally, offering an exclusive 
                  collection of the world's finest automobiles, backed by unparalleled service 
                  and expertise.
                </p>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-luxury-gold/20 to-luxury-darkGold/20 rounded-2xl p-8 h-96 flex items-center justify-center">
                <div className="text-center">
                  <Award className="w-24 h-24 text-luxury-gold mx-auto mb-4" />
                  <h3 className="text-2xl font-luxury font-bold text-white mb-2">
                    Excellence Recognized
                  </h3>
                  <p className="text-gray-300">
                    Award-winning dealership with industry recognition
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-luxury-charcoal">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-luxury font-bold text-white mb-6">
              Our <span className="text-gradient">Achievements</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Numbers that reflect our commitment to excellence and customer satisfaction.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-luxury-gold mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-luxury-darkGray">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-luxury font-bold text-white mb-6">
              Our <span className="text-gradient">Values</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              The principles that guide every interaction and decision at Respect Motors.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-luxury-charcoal rounded-2xl p-8 text-center group hover:bg-luxury-charcoal/80 transition-colors duration-300"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-luxury-gold/10 rounded-full mb-6 group-hover:bg-luxury-gold/20 transition-colors duration-300">
                  <value.icon className="w-8 h-8 text-luxury-gold" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>
                <p className="text-gray-400 leading-relaxed">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-luxury-charcoal">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-4xl font-luxury font-bold text-white mb-8">
              Our <span className="text-gradient">Mission</span>
            </h2>
            <div className="bg-gradient-to-r from-luxury-gold/10 to-luxury-darkGold/10 rounded-2xl p-8 md:p-12">
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed font-light">
                "To provide an unparalleled luxury automotive experience that exceeds expectations, 
                connecting passionate enthusiasts with the world's finest vehicles through 
                exceptional service, expertise, and integrity."
              </p>
              <div className="mt-8 text-luxury-gold font-semibold">
                — Marcus Respect, Founder & CEO
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
